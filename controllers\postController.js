const Post = require("../models/postModel.js");

exports.createPost = async(req, res)=>{
    try{
        const {title, body} = req.body;
        const post = new Post({
            title, body,
        });
        const savePost = await post.save();
        res.json({
            post:savePost,
            message: "Post created successfully",
            status: "success",
            statusCode: 200,
        })
    }
    catch(error){
        return res.status(500).json({
            error: "Failed to create post",
        })
    }
}