const mongoose = require("mongoose");
require("dotenv").config();

const connectDB = async () => {
        mongoose.connect(process.env.DATABASE_URL)
        .then( ()=>
            {console.log("DB connected successfully")
                mongoose.connection.on("error", (err) => {
                    console.log(err);
                    process.exit(1);
                });
            })
        .catch((error)=>{
            console.log(error);
            process.exit(1);
        })
}
module.exports = connectDB;
