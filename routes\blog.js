const express = require("express");
const router = express.Router();

// Import Controllers

const likeController = require("../controllers/likeController.js");
const commentController = require("../controllers/commentController.js");
const postController = require("../controllers/postController.js");

// Mapping Create
router.get("/dummy", likeController.dummyLink);
router.post("/comments/create", commentController.createComment);
router.post("/posts/create", postController.createPost);



// exports
module.exports = router;