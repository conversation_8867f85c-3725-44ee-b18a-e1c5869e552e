const express = require("express");
const app = express();

require("dotenv").config();
const PORT = process.env.PORT || 3000;

// middleware
app.use(express.json());
const blog = require("./routes/blog.js");
// mount
app.use("/api/v1", blog);
// const dummy = require("./routes/dummy.js");
// app.use("/api/v1",dummy);

const connectWithDB = require("./config/database.js");
connectWithDB();

app.listen(PORT, () =>{
    console.log(`Server started on port ${PORT}`);
})

app.get("/", (req,res)=>{
    res.send("Hello vai!"); 
})